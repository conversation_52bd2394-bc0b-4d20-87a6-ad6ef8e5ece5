defmodule RepobotWeb.Live.SourceFiles.EditTest do
  use RepobotWeb.ConnCase, async: true
  use Repobot.Test.Fixtures

  import Phoenix.LiveViewTest
  import Mox

  setup do
    user = create_user()

    source_file =
      create_source_file(%{
        name: "test.ex",
        content: "test content",
        target_path: "lib/test.ex",
        user_id: user.id,
        organization_id: user.default_organization_id
      })

    conn =
      build_conn()
      |> init_test_session(%{current_user_id: user.id})
      |> assign(:current_user, user)

    {:ok, %{user: user, source_file: source_file, conn: conn}}
  end

  describe "edit" do
    test "displays edit form with source file details", %{conn: conn, source_file: source_file} do
      {:ok, view, html} = live(conn, ~p"/source-files/#{source_file}/edit")

      # Verify page title and breadcrumbs
      assert html =~ "Edit Source File"
      assert html =~ source_file.name
      assert has_element?(view, "nav a", "Source Files")
      assert has_element?(view, "nav li", "Edit")

      # Verify form elements
      assert has_element?(view, "form")
      assert has_element?(view, "h1", "Edit Source File")
      assert has_element?(view, "p", "Update your source file content and settings")

      # Verify form fields
      assert has_element?(view, "input#source-file-name")
      assert has_element?(view, "input#source-file-target-path")
      assert has_element?(view, "input#source-file-is-template")
      assert has_element?(view, "textarea", source_file.content)
    end

    test "navigates back to source files list", %{conn: conn, source_file: source_file} do
      {:ok, view, _html} = live(conn, ~p"/source-files/#{source_file}/edit")

      # Click the Source Files link in breadcrumbs
      {:ok, _view, html} =
        view
        |> element("a", "Source Files")
        |> render_click()
        |> follow_redirect(conn)

      assert html =~ "Source Files"
      assert html =~ "A list of all your source files"
    end

    test "navigates back to source file details", %{conn: conn, source_file: source_file} do
      {:ok, view, _html} = live(conn, ~p"/source-files/#{source_file}/edit")

      # Click the source file name link in breadcrumbs
      {:ok, _view, html} =
        view
        |> element("a", source_file.name)
        |> render_click()
        |> follow_redirect(conn)

      assert html =~ source_file.name
      assert html =~ "Source Files"
    end

    test "updates source file with valid data", %{conn: conn, source_file: source_file} do
      {:ok, view, _html} = live(conn, ~p"/source-files/#{source_file}/edit")

      view
      |> form("#source-file-form", %{
        source_file: %{
          name: "updated_test.ex",
          content: "updated content",
          target_path: "lib/updated_test.ex",
          is_template: true,
          tags: "elixir, test"
        }
      })
      |> render_submit()

      assert render(view) =~ "Source file updated successfully"
    end

    test "shows validation errors", %{conn: conn, source_file: source_file} do
      {:ok, view, _html} = live(conn, ~p"/source-files/#{source_file}/edit")

      # Submit the form with invalid data (empty name)
      html =
        view
        |> form("#source-file-form", %{
          source_file: %{
            name: "",
            content: "test content",
            target_path: "lib/test.ex"
          }
        })
        |> render_change()

      assert html =~ "can&#39;t be blank"
    end

    test "shows template variables modal when clicking the help button", %{
      conn: conn,
      source_file: source_file
    } do
      {:ok, view, _html} = live(conn, ~p"/source-files/#{source_file}/edit")

      # Initially, check that the help button is present
      assert has_element?(view, "button[phx-click=\"toggle_template_modal\"]")

      # Click the help button
      html = view |> element("button[phx-click=\"toggle_template_modal\"]") |> render_click()

      # Verify that clicking the button shows the modal with template variables
      assert html =~ "Template Variables"
      assert html =~ "Use these variables in your template"
    end

    test "validates tags format", %{conn: conn, source_file: source_file} do
      {:ok, view, _html} = live(conn, ~p"/source-files/#{source_file}/edit")

      # Submit form with valid tags
      html =
        view
        |> form("#source-file-form", %{
          source_file: %{
            name: source_file.name,
            content: source_file.content,
            target_path: source_file.target_path,
            tags: "tag1, tag2, tag3"
          }
        })
        |> render_change()

      # No validation errors should be shown
      refute html =~ "is invalid"

      # Submit form with invalid tags (using semicolons instead of commas)
      html =
        view
        |> form("#source-file-form", %{
          source_file: %{
            name: source_file.name,
            content: source_file.content,
            target_path: source_file.target_path,
            tags: "tag1; tag2; tag3"
          }
        })
        |> render_change()

      # Tags should still be processed (semicolons are just treated as part of tag names)
      assert html =~ "tag1; tag2; tag3"
    end

    test "shows correct template conversion warning for files without .liquid extension", %{
      conn: conn,
      user: user
    } do
      # Create a source file with a source repository to trigger the warning
      source_repository = create_repository(%{user_id: user.id, name: "test-repo"})

      source_file =
        create_source_file(%{
          name: "CONTRIBUTING.md",
          content: "test content",
          target_path: "CONTRIBUTING.md",
          user_id: user.id,
          organization_id: user.default_organization_id,
          source_repository_id: source_repository.id
        })

      {:ok, view, _html} = live(conn, ~p"/source-files/#{source_file}/edit")

      # Click the template checkbox to trigger the warning
      html = view |> element("#source-file-is-template") |> render_click()

      # Verify the warning shows correct filenames
      assert html =~ "Template Conversion Warning"
      assert html =~ "Rename the file from"
      assert html =~ "CONTRIBUTING.md"
      assert html =~ "to"
      assert html =~ "CONTRIBUTING.md.liquid"
      assert html =~ "Set the target path to"
      assert html =~ "CONTRIBUTING.md"
    end

    test "shows correct template conversion warning for files already with .liquid extension", %{
      conn: conn,
      user: user
    } do
      # Create a source file with a source repository and .liquid extension
      source_repository = create_repository(%{user_id: user.id, name: "test-repo"})

      source_file =
        create_source_file(%{
          name: "CONTRIBUTING.md.liquid",
          content: "test content",
          target_path: "CONTRIBUTING.md",
          user_id: user.id,
          organization_id: user.default_organization_id,
          source_repository_id: source_repository.id
        })

      {:ok, view, _html} = live(conn, ~p"/source-files/#{source_file}/edit")

      # Click the template checkbox to trigger the warning
      html = view |> element("#source-file-is-template") |> render_click()

      # Verify the warning shows correct filenames (should not double-add .liquid)
      assert html =~ "Template Conversion Warning"
      assert html =~ "Rename the file from"
      assert html =~ "CONTRIBUTING.md"
      assert html =~ "to"
      assert html =~ "CONTRIBUTING.md.liquid"
      assert html =~ "Set the target path to"
      assert html =~ "CONTRIBUTING.md"
      # Should not show double .liquid extension
      refute html =~ "CONTRIBUTING.md.liquid.liquid"
    end

    test "confirms template conversion when user clicks proceed", %{
      conn: conn,
      user: user
    } do
      # Create a source file with a source repository to trigger the warning
      source_repository = create_repository(%{user_id: user.id, name: "test-repo"})

      source_file =
        create_source_file(%{
          name: "CONTRIBUTING.md",
          content: "test content",
          target_path: "CONTRIBUTING.md",
          user_id: user.id,
          organization_id: user.default_organization_id,
          source_repository_id: source_repository.id,
          is_template: false
        })

      # Mock GitHub API calls for file renaming using Git Data API
      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}

      Repobot.Test.GitHubMock
      |> expect(:client, fn owner, repo ->
        assert owner == "test-user"
        assert repo == "test-repo"
        test_client
      end)
      |> expect(:get_file_content, fn ^test_client, owner, repo, path ->
        assert owner == "test-user"
        assert repo == "test-repo"
        assert path == "CONTRIBUTING.md"
        {:ok, "test content", %{"sha" => "test-sha"}}
      end)
      |> expect(:rename_file, fn ^test_client,
                                 owner,
                                 repo,
                                 old_path,
                                 new_path,
                                 content,
                                 message ->
        assert owner == "test-user"
        assert repo == "test-repo"
        assert old_path == "CONTRIBUTING.md"
        assert new_path == "CONTRIBUTING.md.liquid"
        assert content == "test content"
        assert message =~ "Convert CONTRIBUTING.md to template"
        {:ok, "new-commit-sha"}
      end)

      {:ok, view, _html} = live(conn, ~p"/source-files/#{source_file}/edit")

      # Click the template checkbox to trigger the warning
      html = view |> element("#source-file-is-template") |> render_click()

      # Verify the warning is shown
      assert html =~ "Template Conversion Warning"
      assert has_element?(view, "button[phx-click=\"confirm_template_conversion\"]")

      # Click the "Proceed with Conversion" button
      html =
        view |> element("button[phx-click=\"confirm_template_conversion\"]") |> render_click()

      # Verify the warning is hidden and the checkbox is now checked
      refute html =~ "Template Conversion Warning"
      assert has_element?(view, "#source-file-is-template[checked]")

      # Verify the file was actually updated in the database
      updated_source_file = Repobot.SourceFiles.get_source_file!(source_file.id)
      assert updated_source_file.is_template == true
      assert updated_source_file.name == "CONTRIBUTING.md.liquid"
      assert updated_source_file.target_path == "CONTRIBUTING.md"

      # Verify the form shows the updated values
      assert has_element?(view, "input[name='source_file[name]'][value='CONTRIBUTING.md.liquid']")
      assert has_element?(view, "input[name='source_file[target_path]'][value='CONTRIBUTING.md']")
    end

    test "cancels template conversion when user clicks cancel", %{
      conn: conn,
      user: user
    } do
      # Create a source file with a source repository to trigger the warning
      source_repository = create_repository(%{user_id: user.id, name: "test-repo"})

      source_file =
        create_source_file(%{
          name: "CONTRIBUTING.md",
          content: "test content",
          target_path: "CONTRIBUTING.md",
          user_id: user.id,
          organization_id: user.default_organization_id,
          source_repository_id: source_repository.id,
          is_template: false
        })

      {:ok, view, _html} = live(conn, ~p"/source-files/#{source_file}/edit")

      # Click the template checkbox to trigger the warning
      html = view |> element("#source-file-is-template") |> render_click()

      # Verify the warning is shown
      assert html =~ "Template Conversion Warning"
      assert has_element?(view, "button[phx-click=\"cancel_template_conversion\"]")

      # Click the "Cancel" button
      html = view |> element("button[phx-click=\"cancel_template_conversion\"]") |> render_click()

      # Verify the warning is hidden and the checkbox remains unchecked
      refute html =~ "Template Conversion Warning"
      refute has_element?(view, "#source-file-is-template[checked]")
    end

    test "reproduces bug: form state changes when checkbox is clicked without source repository",
         %{
           conn: conn,
           user: user
         } do
      # Create a source file WITHOUT a source repository - this will trigger immediate form update
      source_file =
        create_source_file(%{
          name: "CONTRIBUTING.md",
          content: "test content",
          target_path: "CONTRIBUTING.md",
          user_id: user.id,
          organization_id: user.default_organization_id,
          # No source repository
          source_repository_id: nil,
          is_template: false
        })

      {:ok, view, _html} = live(conn, ~p"/source-files/#{source_file}/edit")

      # Initial state: verify form shows original values
      initial_html = render(view)
      assert initial_html =~ "value=\"CONTRIBUTING.md\""
      assert initial_html =~ "value=\"CONTRIBUTING.md\""

      # Step 1: Check the template checkbox - this should immediately update form state
      html = view |> element("#source-file-is-template") |> render_click()

      # No warning should be shown since there's no source repository
      refute html =~ "Template Conversion Warning"

      # Step 2: Check if the form state has changed after clicking the checkbox
      # This is where the issue occurs - the form updates immediately
      form_html_after_click = render(view)

      # Debug: print the form HTML to see what's happening
      IO.puts("Form HTML after checkbox click (no source repo):")
      IO.puts(form_html_after_click)

      # The form should now show the checkbox as checked
      assert has_element?(view, "#source-file-is-template[checked]")

      # Step 3: Now manually change the target_path to simulate user interaction
      view
      |> form("#source-file-form", %{
        source_file: %{
          target_path: "CONTRIBUTING.md.liquid"
        }
      })
      |> render_change()

      # Step 4: Now the user submits the form with the current form state
      # The form shows name="CONTRIBUTING.md.liquid" and target_path="CONTRIBUTING.md"
      # But there might be an issue where the form submission includes the wrong values

      # Let's check what the form actually contains
      form_data = view |> form("#source-file-form") |> render_submit()

      # Debug: Check what was actually submitted
      IO.puts("Form submission result:")
      IO.puts(form_data)

      # Verify the file was updated in the database
      updated_source_file = Repobot.SourceFiles.get_source_file!(source_file.id)
      assert updated_source_file.is_template == true
      assert updated_source_file.name == "CONTRIBUTING.md.liquid"

      # The target_path should be correctly set to the original name
      assert updated_source_file.target_path == "CONTRIBUTING.md"
    end

    test "correctly handles template conversion when target path is manually set to .liquid filename",
         %{
           conn: conn,
           user: user
         } do
      # Create a source file with a source repository to trigger the warning
      source_repository = create_repository(%{user_id: user.id, name: "test-repo"})

      source_file =
        create_source_file(%{
          name: "CONTRIBUTING.md",
          content: "test content",
          target_path: "CONTRIBUTING.md",
          user_id: user.id,
          organization_id: user.default_organization_id,
          source_repository_id: source_repository.id,
          is_template: false
        })

      # Mock GitHub API calls for file renaming using Git Data API
      test_client = %Tentacat.Client{auth: %{access_token: "test_token"}}

      Repobot.Test.GitHubMock
      |> expect(:client, fn owner, repo ->
        assert owner == "test-user"
        assert repo == "test-repo"
        test_client
      end)
      |> expect(:get_file_content, fn ^test_client, owner, repo, path ->
        assert owner == "test-user"
        assert repo == "test-repo"
        assert path == "CONTRIBUTING.md"
        {:ok, "test content", %{"sha" => "test-sha"}}
      end)
      |> expect(:rename_file, fn ^test_client,
                                 owner,
                                 repo,
                                 old_path,
                                 new_path,
                                 content,
                                 message ->
        assert owner == "test-user"
        assert repo == "test-repo"
        assert old_path == "CONTRIBUTING.md"
        assert new_path == "CONTRIBUTING.md.liquid"
        assert content == "test content"
        assert message =~ "Convert CONTRIBUTING.md to template"
        {:ok, "new-commit-sha"}
      end)

      {:ok, view, _html} = live(conn, ~p"/source-files/#{source_file}/edit")

      # First, manually change the target_path to include .liquid extension to simulate the bug
      view
      |> form("#source-file-form", %{
        source_file: %{
          target_path: "CONTRIBUTING.md.liquid"
        }
      })
      |> render_change()

      # Now click the template checkbox to trigger the warning
      _html = view |> element("#source-file-is-template") |> render_click()

      # Verify the warning is shown
      assert has_element?(view, "button[phx-click=\"confirm_template_conversion\"]")

      # Click the "Proceed with Conversion" button
      _html =
        view |> element("button[phx-click=\"confirm_template_conversion\"]") |> render_click()

      # Verify the file was updated in the database
      updated_source_file = Repobot.SourceFiles.get_source_file!(source_file.id)
      assert updated_source_file.is_template == true
      assert updated_source_file.name == "CONTRIBUTING.md.liquid"

      # The target_path should be correctly set to the original filename without .liquid extension
      assert updated_source_file.target_path == "CONTRIBUTING.md"
    end
  end
end
